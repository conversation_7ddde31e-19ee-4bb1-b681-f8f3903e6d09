// Utility function for overtime calculations
// This can be imported and used across different components

export interface OvertimeBreakdown {
  tier1: { hours: number; rate: number; pay: number };
  tier2: { hours: number; rate: number; pay: number };
  tier3: { hours: number; rate: number; pay: number };
  totalHours: number;
  totalPay: number;
}

/**
 * Calculate overtime earnings with tiered rates
 * @param overtimeHours - Total overtime hours worked
 * @param basicPayPerHour - Base hourly pay rate
 * @returns Detailed breakdown of overtime pay by tier
 */
export function calculateOvertimeEarnings(
  overtimeHours: number, 
  basicPayPerHour: number
): OvertimeBreakdown {
  let totalOvertimePay = 0;
  let breakdown: OvertimeBreakdown = {
    tier1: { hours: 0, rate: 0, pay: 0 }, // ≤20 hours: 80% rate
    tier2: { hours: 0, rate: 0, pay: 0 }, // 21-40 hours: 75% rate  
    tier3: { hours: 0, rate: 0, pay: 0 }, // 41-60 hours: 70% rate
    totalHours: overtimeHours,
    totalPay: 0
  };

  if (overtimeHours <= 0) return breakdown;

  // Tier 1: First 20 hours at 80% rate
  if (overtimeHours > 0) {
    const tier1Hours = Math.min(overtimeHours, 20);
    const tier1Rate = basicPayPerHour * 0.8;
    const tier1Pay = tier1Hours * tier1Rate;
    
    breakdown.tier1 = {
      hours: tier1Hours,
      rate: tier1Rate,
      pay: tier1Pay
    };
    totalOvertimePay += tier1Pay;
  }

  // Tier 2: Next 20 hours (21-40) at 75% rate
  if (overtimeHours > 20) {
    const tier2Hours = Math.min(overtimeHours - 20, 20);
    const tier2Rate = basicPayPerHour * 0.75;
    const tier2Pay = tier2Hours * tier2Rate;
    
    breakdown.tier2 = {
      hours: tier2Hours,
      rate: tier2Rate,
      pay: tier2Pay
    };
    totalOvertimePay += tier2Pay;
  }

  // Tier 3: Next 20 hours (41-60) at 70% rate
  if (overtimeHours > 40) {
    const tier3Hours = Math.min(overtimeHours - 40, 20);
    const tier3Rate = basicPayPerHour * 0.7;
    const tier3Pay = tier3Hours * tier3Rate;
    
    breakdown.tier3 = {
      hours: tier3Hours,
      rate: tier3Rate,
      pay: tier3Pay
    };
    totalOvertimePay += tier3Pay;
  }

  breakdown.totalPay = totalOvertimePay;
  return breakdown;
}

/**
 * Test function to verify overtime calculations
 */
export function testOvertimeCalculations() {
  const basicRate = 25; // $25/hour base rate
  
  console.log('=== Overtime Calculator Tests ===');
  
  // Test Case 1: 0 hours
  console.log('\nTest 1: 0 hours');
  const test1 = calculateOvertimeEarnings(0, basicRate);
  console.log('Expected: $0, Got:', test1.totalPay);
  console.log('Breakdown:', test1);
  
  // Test Case 2: 10 hours (Tier 1 only)
  console.log('\nTest 2: 10 hours (Tier 1 only)');
  const test2 = calculateOvertimeEarnings(10, basicRate);
  const expected2 = 10 * (basicRate * 0.8); // 10 * $20 = $200
  console.log(`Expected: $${expected2}, Got: $${test2.totalPay}`);
  console.log('Breakdown:', test2);
  
  // Test Case 3: 20 hours (Tier 1 max)
  console.log('\nTest 3: 20 hours (Tier 1 max)');
  const test3 = calculateOvertimeEarnings(20, basicRate);
  const expected3 = 20 * (basicRate * 0.8); // 20 * $20 = $400
  console.log(`Expected: $${expected3}, Got: $${test3.totalPay}`);
  console.log('Breakdown:', test3);
  
  // Test Case 4: 30 hours (Tier 1 + Tier 2)
  console.log('\nTest 4: 30 hours (Tier 1 + Tier 2)');
  const test4 = calculateOvertimeEarnings(30, basicRate);
  const expected4 = (20 * basicRate * 0.8) + (10 * basicRate * 0.75); // $400 + $187.5 = $587.5
  console.log(`Expected: $${expected4}, Got: $${test4.totalPay}`);
  console.log('Breakdown:', test4);
  
  // Test Case 5: 40 hours (Tier 1 + Tier 2 max)
  console.log('\nTest 5: 40 hours (Tier 1 + Tier 2 max)');
  const test5 = calculateOvertimeEarnings(40, basicRate);
  const expected5 = (20 * basicRate * 0.8) + (20 * basicRate * 0.75); // $400 + $375 = $775
  console.log(`Expected: $${expected5}, Got: $${test5.totalPay}`);
  console.log('Breakdown:', test5);
  
  // Test Case 6: 50 hours (All tiers)
  console.log('\nTest 6: 50 hours (All tiers)');
  const test6 = calculateOvertimeEarnings(50, basicRate);
  const expected6 = (20 * basicRate * 0.8) + (20 * basicRate * 0.75) + (10 * basicRate * 0.7); 
  // $400 + $375 + $175 = $950
  console.log(`Expected: $${expected6}, Got: $${test6.totalPay}`);
  console.log('Breakdown:', test6);
  
  // Test Case 7: 60 hours (All tiers max)
  console.log('\nTest 7: 60 hours (All tiers max)');
  const test7 = calculateOvertimeEarnings(60, basicRate);
  const expected7 = (20 * basicRate * 0.8) + (20 * basicRate * 0.75) + (20 * basicRate * 0.7); 
  // $400 + $375 + $350 = $1125
  console.log(`Expected: $${expected7}, Got: $${test7.totalPay}`);
  console.log('Breakdown:', test7);
  
  // Test Case 8: Edge case with decimals
  console.log('\nTest 8: 15.5 hours (Tier 1 with decimals)');
  const test8 = calculateOvertimeEarnings(15.5, basicRate);
  const expected8 = 15.5 * (basicRate * 0.8); // 15.5 * $20 = $310
  console.log(`Expected: $${expected8}, Got: $${test8.totalPay}`);
  console.log('Breakdown:', test8);
  
  console.log('\n=== Tests Complete ===');
  
  return {
    test1, test2, test3, test4, test5, test6, test7, test8
  };
}

/**
 * Validate that all calculations are correct
 */
export function validateOvertimeCalculations(): boolean {
  const basicRate = 25;
  let allTestsPassed = true;
  
  // Test cases with expected results
  const testCases = [
    { hours: 0, expected: 0 },
    { hours: 10, expected: 10 * (basicRate * 0.8) },
    { hours: 20, expected: 20 * (basicRate * 0.8) },
    { hours: 30, expected: (20 * basicRate * 0.8) + (10 * basicRate * 0.75) },
    { hours: 40, expected: (20 * basicRate * 0.8) + (20 * basicRate * 0.75) },
    { hours: 50, expected: (20 * basicRate * 0.8) + (20 * basicRate * 0.75) + (10 * basicRate * 0.7) },
    { hours: 60, expected: (20 * basicRate * 0.8) + (20 * basicRate * 0.75) + (20 * basicRate * 0.7) }
  ];
  
  testCases.forEach((testCase, index) => {
    const result = calculateOvertimeEarnings(testCase.hours, basicRate);
    const passed = Math.abs(result.totalPay - testCase.expected) < 0.01; // Allow for floating point precision
    
    if (!passed) {
      console.error(`Test ${index + 1} FAILED: ${testCase.hours} hours`);
      console.error(`Expected: $${testCase.expected}, Got: $${result.totalPay}`);
      allTestsPassed = false;
    } else {
      console.log(`Test ${index + 1} PASSED: ${testCase.hours} hours = $${result.totalPay}`);
    }
  });
  
  return allTestsPassed;
}
