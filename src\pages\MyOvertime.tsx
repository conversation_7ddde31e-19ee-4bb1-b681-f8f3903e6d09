import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../lib/store';
import OvertimeView from '../components/OvertimeView';
import { Calendar, ArrowLeft } from 'lucide-react';

export default function MyOvertime() {
  const user = useAuthStore((state) => state.user);
  const [selectedMonth, setSelectedMonth] = useState<string>('');
  const [showAllTime, setShowAllTime] = useState(true);

  // Get current month in YYYY-MM format
  const getCurrentMonth = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  };

  useEffect(() => {
    setSelectedMonth(getCurrentMonth());
  }, []);

  const handleMonthChange = (month: string) => {
    setSelectedMonth(month);
    setShowAllTime(false);
  };

  const handleShowAllTime = () => {
    setShowAllTime(true);
    setSelectedMonth('');
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Please log in to view your overtime information.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => window.history.back()}
                className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-800">My Overtime</h1>
                <p className="text-gray-600">Track your overtime hours and earnings</p>
              </div>
            </div>
            
            {/* Month Selector */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Calendar className="w-5 h-5 text-gray-600" />
                <select
                  value={showAllTime ? '' : selectedMonth}
                  onChange={(e) => {
                    if (e.target.value === '') {
                      handleShowAllTime();
                    } else {
                      handleMonthChange(e.target.value);
                    }
                  }}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Time</option>
                  {/* Generate last 12 months */}
                  {Array.from({ length: 12 }, (_, i) => {
                    const date = new Date();
                    date.setMonth(date.getMonth() - i);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const value = `${year}-${month}`;
                    const label = date.toLocaleDateString('en-US', { 
                      year: 'numeric', 
                      month: 'long' 
                    });
                    return (
                      <option key={value} value={value}>
                        {label}
                      </option>
                    );
                  })}
                </select>
              </div>
              
              <button
                onClick={handleShowAllTime}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  showAllTime
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                All Time
              </button>
            </div>
          </div>
        </div>

        {/* Overtime Information */}
        <div className="space-y-6">
          {/* Current View Info */}
          <div className="bg-white rounded-lg shadow-sm p-4 border-l-4 border-blue-500">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-800">
                  {showAllTime ? 'All Time Overview' : `${new Date(selectedMonth + '-01').toLocaleDateString('en-US', { year: 'numeric', month: 'long' })} Overview`}
                </h3>
                <p className="text-sm text-gray-600">
                  {showAllTime 
                    ? 'Complete overtime history and earnings'
                    : 'Monthly overtime breakdown with detailed rate structure'
                  }
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-500">Viewing</p>
                <p className="font-medium text-blue-600">
                  {showAllTime ? 'All Time' : selectedMonth}
                </p>
              </div>
            </div>
          </div>

          {/* Overtime Component */}
          <OvertimeView 
            userId={user.id} 
            selectedMonth={showAllTime ? undefined : selectedMonth}
          />

          {/* Information Panel */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">How Overtime is Calculated</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center mb-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                  <h4 className="font-medium text-blue-800">Tier 1 (1-20 hours)</h4>
                </div>
                <p className="text-sm text-blue-700">
                  First 20 overtime hours are paid at 80% of your base hourly rate.
                </p>
              </div>
              
              <div className="p-4 bg-yellow-50 rounded-lg">
                <div className="flex items-center mb-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                  <h4 className="font-medium text-yellow-800">Tier 2 (21-40 hours)</h4>
                </div>
                <p className="text-sm text-yellow-700">
                  Hours 21-40 are paid at 75% of your base hourly rate.
                </p>
              </div>
              
              <div className="p-4 bg-red-50 rounded-lg">
                <div className="flex items-center mb-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                  <h4 className="font-medium text-red-800">Tier 3 (41-60 hours)</h4>
                </div>
                <p className="text-sm text-red-700">
                  Hours 41-60 are paid at 70% of your base hourly rate.
                </p>
              </div>
            </div>
            
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-800 mb-2">Important Notes:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Overtime hours are calculated from the "extrahours" table</li>
                <li>• Break times are automatically deducted from total hours</li>
                <li>• Only completed overtime sessions (with check-out time) are counted</li>
                <li>• Rates are based on your current base hourly pay rate</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
