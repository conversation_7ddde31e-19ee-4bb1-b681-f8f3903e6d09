import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { Clock, DollarSign, TrendingUp, Calendar } from 'lucide-react';

interface OvertimeBreakdown {
  tier1: { hours: number; rate: number; pay: number };
  tier2: { hours: number; rate: number; pay: number };
  tier3: { hours: number; rate: number; pay: number };
  totalHours: number;
  totalPay: number;
}

interface OvertimeData {
  totalOvertimeHours: number;
  overtimePay: string;
  overtimeBreakdown: OvertimeBreakdown;
  basicPayPerHour: number;
}

interface OvertimeViewProps {
  userId: string;
  selectedMonth?: string; // Format: YYYY-MM
}

export default function OvertimeView({ userId, selectedMonth }: OvertimeViewProps) {
  const [overtimeData, setOvertimeData] = useState<OvertimeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Enhanced overtime calculation function
  const calculateOvertimeEarnings = (overtimeHours: number, basicPayPerHour: number): OvertimeBreakdown => {
    let totalOvertimePay = 0;
    let breakdown: OvertimeBreakdown = {
      tier1: { hours: 0, rate: 0, pay: 0 }, // ≤20 hours: 80% rate
      tier2: { hours: 0, rate: 0, pay: 0 }, // 21-40 hours: 75% rate  
      tier3: { hours: 0, rate: 0, pay: 0 }, // 41-60 hours: 70% rate
      totalHours: overtimeHours,
      totalPay: 0
    };

    if (overtimeHours <= 0) return breakdown;

    // Tier 1: First 20 hours at 80% rate
    if (overtimeHours > 0) {
      const tier1Hours = Math.min(overtimeHours, 20);
      const tier1Rate = basicPayPerHour * 0.8;
      const tier1Pay = tier1Hours * tier1Rate;
      
      breakdown.tier1 = {
        hours: tier1Hours,
        rate: tier1Rate,
        pay: tier1Pay
      };
      totalOvertimePay += tier1Pay;
    }

    // Tier 2: Next 20 hours (21-40) at 75% rate
    if (overtimeHours > 20) {
      const tier2Hours = Math.min(overtimeHours - 20, 20);
      const tier2Rate = basicPayPerHour * 0.75;
      const tier2Pay = tier2Hours * tier2Rate;
      
      breakdown.tier2 = {
        hours: tier2Hours,
        rate: tier2Rate,
        pay: tier2Pay
      };
      totalOvertimePay += tier2Pay;
    }

    // Tier 3: Next 20 hours (41-60) at 70% rate
    if (overtimeHours > 40) {
      const tier3Hours = Math.min(overtimeHours - 40, 20);
      const tier3Rate = basicPayPerHour * 0.7;
      const tier3Pay = tier3Hours * tier3Rate;
      
      breakdown.tier3 = {
        hours: tier3Hours,
        rate: tier3Rate,
        pay: tier3Pay
      };
      totalOvertimePay += tier3Pay;
    }

    breakdown.totalPay = totalOvertimePay;
    return breakdown;
  };

  useEffect(() => {
    fetchOvertimeData();
  }, [userId, selectedMonth]);

  const fetchOvertimeData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get user data for basic pay per hour
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('per_hour_pay')
        .eq('id', userId)
        .single();

      if (userError) throw userError;

      const basicPayPerHour = userData.per_hour_pay ? parseFloat(userData.per_hour_pay) : 0;

      // Set date range based on selected month or all time
      let startDate: string | undefined;
      let endDate: string | undefined;

      if (selectedMonth) {
        const [year, month] = selectedMonth.split('-').map(Number);
        const startOfMonth = new Date(Date.UTC(year, month - 1, 1));
        const endOfMonth = new Date(Date.UTC(year, month, 0, 23, 59, 59, 999));
        startDate = startOfMonth.toISOString();
        endDate = endOfMonth.toISOString();
      }

      // Fetch overtime data from extrahours table
      let query = supabase
        .from('extrahours')
        .select('id, check_in, check_out')
        .eq('user_id', userId);

      if (startDate && endDate) {
        query = query.gte('check_in', startDate).lte('check_in', endDate);
      }

      const { data: extrahoursData, error: extrahoursError } = await query;

      if (extrahoursError) throw extrahoursError;

      // Fetch breaks for extrahours
      const { data: remoteBreakData, error: remoteBreakError } = await supabase
        .from('Remote_Breaks')
        .select('start_time, end_time, Remote_Id')
        .in('Remote_Id', extrahoursData.map(a => a.id));

      if (remoteBreakError) {
        console.error('Error fetching remote breaks:', remoteBreakError);
      }

      // Group remote breaks by Remote_Id
      const remoteBreaksByAttendance: { [key: string]: any[] } = {};
      if (remoteBreakData) {
        remoteBreakData.forEach(b => {
          if (!remoteBreaksByAttendance[b.Remote_Id]) remoteBreaksByAttendance[b.Remote_Id] = [];
          remoteBreaksByAttendance[b.Remote_Id].push(b);
        });
      }

      // Calculate total overtime hours
      let totalOvertimeHours = 0;
      extrahoursData.forEach(log => {
        if (log.check_in && log.check_out) {
          const checkIn = new Date(log.check_in);
          const checkOut = new Date(log.check_out);

          let hoursWorked = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);

          // Subtract remote breaks
          const remoteBreaks = remoteBreaksByAttendance[log.id] || [];
          let remoteBreakHours = 0;

          remoteBreaks.forEach(b => {
            if (b.start_time && b.end_time) {
              const breakStart = new Date(b.start_time);
              const breakEnd = new Date(b.end_time);
              remoteBreakHours += (breakEnd.getTime() - breakStart.getTime()) / (1000 * 60 * 60);
            }
          });

          totalOvertimeHours += Math.max(0, hoursWorked - remoteBreakHours);
        }
      });

      // Calculate overtime breakdown
      const overtimeBreakdown = calculateOvertimeEarnings(totalOvertimeHours, basicPayPerHour);

      setOvertimeData({
        totalOvertimeHours,
        overtimePay: overtimeBreakdown.totalPay.toFixed(2),
        overtimeBreakdown,
        basicPayPerHour
      });

    } catch (err: any) {
      setError(err.message || 'Failed to fetch overtime data');
      console.error('Error fetching overtime data:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-red-600 text-center">
          <p>Error: {error}</p>
        </div>
      </div>
    );
  }

  if (!overtimeData) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-gray-500 text-center">
          <p>No overtime data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Clock className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-800">
            Overtime Summary {selectedMonth && `- ${selectedMonth}`}
          </h2>
        </div>
        {selectedMonth && (
          <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
            Monthly View
          </span>
        )}
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-600 text-sm font-medium">Total Hours</p>
              <p className="text-2xl font-bold text-blue-800">
                {overtimeData.totalOvertimeHours.toFixed(1)}
              </p>
            </div>
            <Clock className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-600 text-sm font-medium">Total Earnings</p>
              <p className="text-2xl font-bold text-green-800">
                ${overtimeData.overtimePay}
              </p>
            </div>
            <DollarSign className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-600 text-sm font-medium">Base Rate</p>
              <p className="text-2xl font-bold text-purple-800">
                ${overtimeData.basicPayPerHour.toFixed(2)}/hr
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Detailed Breakdown */}
      {overtimeData.totalOvertimeHours > 0 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
            <Calendar className="w-5 h-5 mr-2" />
            Overtime Rate Breakdown
          </h3>
          
          <div className="space-y-3">
            {overtimeData.overtimeBreakdown.tier1.hours > 0 && (
              <div className="flex justify-between items-center p-3 bg-white rounded-lg border-l-4 border-blue-500">
                <div>
                  <p className="font-medium text-gray-800">
                    First {overtimeData.overtimeBreakdown.tier1.hours.toFixed(1)} hours
                  </p>
                  <p className="text-sm text-gray-600">
                    80% rate (${overtimeData.overtimeBreakdown.tier1.rate.toFixed(2)}/hr)
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-bold text-blue-600">
                    ${overtimeData.overtimeBreakdown.tier1.pay.toFixed(2)}
                  </p>
                </div>
              </div>
            )}

            {overtimeData.overtimeBreakdown.tier2.hours > 0 && (
              <div className="flex justify-between items-center p-3 bg-white rounded-lg border-l-4 border-yellow-500">
                <div>
                  <p className="font-medium text-gray-800">
                    Next {overtimeData.overtimeBreakdown.tier2.hours.toFixed(1)} hours
                  </p>
                  <p className="text-sm text-gray-600">
                    75% rate (${overtimeData.overtimeBreakdown.tier2.rate.toFixed(2)}/hr)
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-bold text-yellow-600">
                    ${overtimeData.overtimeBreakdown.tier2.pay.toFixed(2)}
                  </p>
                </div>
              </div>
            )}

            {overtimeData.overtimeBreakdown.tier3.hours > 0 && (
              <div className="flex justify-between items-center p-3 bg-white rounded-lg border-l-4 border-red-500">
                <div>
                  <p className="font-medium text-gray-800">
                    Next {overtimeData.overtimeBreakdown.tier3.hours.toFixed(1)} hours
                  </p>
                  <p className="text-sm text-gray-600">
                    70% rate (${overtimeData.overtimeBreakdown.tier3.rate.toFixed(2)}/hr)
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-bold text-red-600">
                    ${overtimeData.overtimeBreakdown.tier3.pay.toFixed(2)}
                  </p>
                </div>
              </div>
            )}

            <div className="flex justify-between items-center p-3 bg-gray-800 text-white rounded-lg font-bold">
              <div>
                <p>Total Overtime</p>
                <p className="text-sm opacity-80">
                  {overtimeData.overtimeBreakdown.totalHours.toFixed(1)} hours
                </p>
              </div>
              <div className="text-right">
                <p className="text-xl">${overtimeData.overtimeBreakdown.totalPay.toFixed(2)}</p>
              </div>
            </div>
          </div>

          {/* Rate Information */}
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-800 mb-2">Rate Structure</h4>
            <div className="text-sm text-blue-700 space-y-1">
              <p>• Hours 1-20: 80% of base rate (${(overtimeData.basicPayPerHour * 0.8).toFixed(2)}/hr)</p>
              <p>• Hours 21-40: 75% of base rate (${(overtimeData.basicPayPerHour * 0.75).toFixed(2)}/hr)</p>
              <p>• Hours 41-60: 70% of base rate (${(overtimeData.basicPayPerHour * 0.7).toFixed(2)}/hr)</p>
            </div>
          </div>
        </div>
      )}

      {overtimeData.totalOvertimeHours === 0 && (
        <div className="text-center py-8">
          <Clock className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500 text-lg">No overtime hours recorded</p>
          <p className="text-gray-400 text-sm">
            {selectedMonth ? 'for the selected month' : 'yet'}
          </p>
        </div>
      )}
    </div>
  );
}
